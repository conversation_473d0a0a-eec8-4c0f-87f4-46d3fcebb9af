2025-05-31 06:00:46,991 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:00:47,440 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/private/account/assets HTTP/1.1" 200 330
2025-05-31 06:00:58,351 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:00:58,828 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/private/account/assets HTTP/1.1" 200 330
2025-05-31 06:02:40,594 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:41,045 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/private/account/assets HTTP/1.1" 200 330
2025-05-31 06:02:41,162 - api - DEBUG - <PERSON><PERSON> đăng ký lệnh: start
2025-05-31 06:02:41,162 - api - DEBUG - <PERSON><PERSON> đăng ký lệnh: help
2025-05-31 06:02:41,163 - api - DEBUG - Đã đăng ký lệnh: keyboard
2025-05-31 06:02:41,163 - api - DEBUG - on_ready has successfully been registered as an event
2025-05-31 06:02:41,163 - api - DEBUG - on_message has successfully been registered as an event
2025-05-31 06:02:41,163 - api - INFO - DiscordHandler đã được khởi tạo
2025-05-31 06:02:41,163 - api - DEBUG - Đã đăng ký lệnh: status
2025-05-31 06:02:41,163 - api - DEBUG - Đã đăng ký lệnh: balance
2025-05-31 06:02:41,163 - api - DEBUG - Đã đăng ký lệnh: stats
2025-05-31 06:02:41,163 - api - DEBUG - Đã đăng ký lệnh: orders
2025-05-31 06:02:41,163 - api - DEBUG - Đã đăng ký lệnh: watchlist
2025-05-31 06:02:41,163 - api - DEBUG - Đã đăng ký lệnh: openpositions
2025-05-31 06:02:41,164 - api - DEBUG - Đã đăng ký lệnh: settings
2025-05-31 06:02:41,164 - api - DEBUG - Đã đăng ký lệnh: clearsymbol
2025-05-31 06:02:41,164 - api - DEBUG - Đã đăng ký lệnh: pause
2025-05-31 06:02:41,164 - api - DEBUG - Đã đăng ký lệnh: resume
2025-05-31 06:02:41,164 - api - DEBUG - Đã đăng ký lệnh: updatesymbols
2025-05-31 06:02:41,165 - api - INFO - Đã bắt đầu Discord bot
2025-05-31 06:02:41,165 - api - INFO - Khởi động Trading Bot...
2025-05-31 06:02:41,165 - api - WARNING - Bot chưa sẵn sàng
2025-05-31 06:02:41,166 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:41,167 - api - DEBUG - Using selector: EpollSelector
2025-05-31 06:02:41,168 - api - INFO - logging in using static token
2025-05-31 06:02:41,464 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/BTC_USDT?interval=Min1&start=1748668061&end=1748671361 HTTP/1.1" 200 1445
2025-05-31 06:02:41,601 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:41,723 - api - DEBUG - GET https://discord.com/api/v10/users/@me with None has returned 200
2025-05-31 06:02:41,723 - api - DEBUG - GET /users/@me has found its initial rate limit bucket hash (78bb8553d9352a5a2f89f9def401287a).
2025-05-31 06:02:41,723 - api - DEBUG - GET https://discord.com/api/v10/users/@me has received {'id': '1377541971005935627', 'username': 'tre', 'avatar': None, 'discriminator': '7616', 'public_flags': 0, 'flags': 0, 'bot': True, 'banner': None, 'accent_color': None, 'global_name': None, 'avatar_decoration_data': None, 'collectibles': None, 'banner_color': None, 'clan': None, 'primary_guild': None, 'mfa_enabled': True, 'locale': 'en-US', 'premium_type': 0, 'email': None, 'verified': True, 'bio': ''}
2025-05-31 06:02:41,875 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/ETH_USDT?interval=Min1&start=1748668061&end=1748671361 HTTP/1.1" 200 1451
2025-05-31 06:02:41,956 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:42,168 - api - DEBUG - GET https://discord.com/api/v10/oauth2/applications/@me with None has returned 200
2025-05-31 06:02:42,168 - api - DEBUG - GET /oauth2/applications/@me has found its initial rate limit bucket hash (d28a133af187e91d71d96a223467ce49).
2025-05-31 06:02:42,168 - api - DEBUG - GET https://discord.com/api/v10/oauth2/applications/@me has received {'id': '1377541971005935627', 'name': 'tre', 'icon': None, 'description': '', 'type': None, 'bot': {'id': '1377541971005935627', 'username': 'tre', 'avatar': None, 'discriminator': '7616', 'public_flags': 0, 'flags': 0, 'bot': True, 'banner': None, 'accent_color': None, 'global_name': None, 'avatar_decoration_data': None, 'collectibles': None, 'banner_color': None, 'clan': None, 'primary_guild': None}, 'summary': '', 'is_monetized': False, 'is_verified': False, 'is_discoverable': False, 'bot_public': True, 'bot_require_code_grant': False, 'install_params': {'scopes': ['applications.commands'], 'permissions': '0'}, 'integration_types_config': {'0': {'oauth2_install_params': {'scopes': ['applications.commands'], 'permissions': '0'}}, '1': {'oauth2_install_params': {'scopes': ['applications.commands'], 'permissions': '0'}}}, 'verify_key': 'dd8dd30e8b903804979995668ecfc28aa85d238f4f34fdbb0357f8f672b74d06', 'flags': 18874368, 'hook': True, 'storefront_available': False, 'redirect_uris': [], 'interactions_endpoint_url': None, 'role_connections_verification_url': None, 'owner': {'id': '1365018104249450540', 'username': 'fantastic_beagle_96376', 'avatar': None, 'discriminator': '0', 'public_flags': 0, 'flags': 0, 'banner': None, 'accent_color': None, 'global_name': 'pl', 'avatar_decoration_data': None, 'collectibles': None, 'banner_color': None, 'clan': None, 'primary_guild': None}, 'approximate_guild_count': 1, 'approximate_user_install_count': 0, 'interactions_event_types': [], 'interactions_version': 1, 'explicit_content_filter': 0, 'rpc_application_state': 0, 'store_application_state': 1, 'verification_state': 1, 'integration_public': True, 'integration_require_code_grant': False, 'discoverability_state': 1, 'discovery_eligibility_flags': 2528, 'monetization_state': 1, 'verification_eligibility_flags': 123804, 'monetization_eligibility_flags': 247524, 'team': None, 'internal_guild_restriction': 2, 'approved_consoles': []}
2025-05-31 06:02:42,251 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/SOL_USDT?interval=Min1&start=1748668061&end=1748671361 HTTP/1.1" 200 1268
2025-05-31 06:02:42,353 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:42,512 - api - DEBUG - Created websocket connected to wss://gateway.discord.gg/
2025-05-31 06:02:42,512 - api - DEBUG - For Shard ID None: WebSocket Event: {'t': None, 's': None, 'op': 10, 'd': {'heartbeat_interval': 41250, '_trace': ['["gateway-prd-us-east1-b-qnzg",{"micros":0.0}]']}}
2025-05-31 06:02:42,513 - api - DEBUG - Shard ID None has sent the IDENTIFY payload.
2025-05-31 06:02:42,642 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/SUI_USDT?interval=Min1&start=1748668062&end=1748671362 HTTP/1.1" 200 1397
2025-05-31 06:02:42,727 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:42,744 - api - DEBUG - For Shard ID None: WebSocket Event: {'t': None, 's': None, 'op': 11, 'd': None}
2025-05-31 06:02:42,800 - api - DEBUG - Received WSMessage(type=<WSMsgType.CLOSE: 8>, data=4014, extra='Disallowed intent(s).')
2025-05-31 06:02:42,801 - api - DEBUG - Websocket closed with 4014, cannot reconnect.
2025-05-31 06:02:42,801 - api - DEBUG - Dispatching event disconnect
2025-05-31 06:02:42,801 - api - ERROR - Lỗi chạy Discord bot: Shard ID None is requesting privileged intents that have not been explicitly enabled in the developer portal. It is recommended to go to https://discord.com/developers/applications/ and explicitly enable the privileged intents within your application's page. If this is not possible, then consider disabling the privileged intents instead.
2025-05-31 06:02:43,071 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/ENA_USDT?interval=Min1&start=1748668062&end=1748671362 HTTP/1.1" 200 1154
2025-05-31 06:02:43,154 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:43,467 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/XRP_USDT?interval=Min1&start=1748668063&end=1748671363 HTTP/1.1" 200 1309
2025-05-31 06:02:43,571 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:43,885 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/ADA_USDT?interval=Min1&start=1748668063&end=1748671363 HTTP/1.1" 200 1257
2025-05-31 06:02:43,973 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:44,267 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/POPCAT_USDT?interval=Min1&start=1748668063&end=1748671363 HTTP/1.1" 200 1182
2025-05-31 06:02:44,354 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:44,687 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/WLD_USDT?interval=Min1&start=1748668064&end=1748671364 HTTP/1.1" 200 1287
2025-05-31 06:02:44,772 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:45,068 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/AVAX_USDT?interval=Min1&start=1748668064&end=1748671364 HTTP/1.1" 200 1310
2025-05-31 06:02:45,152 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:45,444 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/ONDO_USDT?interval=Min1&start=1748668065&end=1748671365 HTTP/1.1" 200 1216
2025-05-31 06:02:45,530 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:45,813 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/LINK_USDT?interval=Min1&start=1748668065&end=1748671365 HTTP/1.1" 200 1305
2025-05-31 06:02:45,921 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:46,250 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/LTC_USDT?interval=Min1&start=1748668065&end=1748671365 HTTP/1.1" 200 1203
2025-05-31 06:02:46,332 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:46,641 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/WIF_USDT?interval=Min1&start=1748668066&end=1748671366 HTTP/1.1" 200 1315
2025-05-31 06:02:46,732 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:47,025 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/ALPHA_USDT?interval=Min1&start=1748668066&end=1748671366 HTTP/1.1" 200 1105
2025-05-31 06:02:47,107 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:47,399 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/DOGS_USDT?interval=Min1&start=1748668067&end=1748671367 HTTP/1.1" 200 1142
2025-05-31 06:02:47,486 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:47,796 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/TAO_USDT?interval=Min1&start=1748668067&end=1748671367 HTTP/1.1" 200 1230
2025-05-31 06:02:47,878 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:48,164 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/TIA_USDT?interval=Min1&start=1748668067&end=1748671367 HTTP/1.1" 200 1064
2025-05-31 06:02:48,253 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:48,542 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/NEAR_USDT?interval=Min1&start=1748668068&end=1748671368 HTTP/1.1" 200 1019
2025-05-31 06:02:48,628 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:48,933 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/INJ_USDT?interval=Min1&start=1748668068&end=1748671368 HTTP/1.1" 200 1230
2025-05-31 06:02:49,022 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:49,481 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/PHB_USDT?interval=Min1&start=1748668069&end=1748671369 HTTP/1.1" 200 1149
2025-05-31 06:02:49,581 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:49,853 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/OP_USDT?interval=Min1&start=1748668069&end=1748671369 HTTP/1.1" 200 1219
2025-05-31 06:02:49,937 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:50,237 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/DOT_USDT?interval=Min1&start=1748668069&end=1748671369 HTTP/1.1" 200 1151
2025-05-31 06:02:50,320 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:50,594 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/FET_USDT?interval=Min1&start=1748668070&end=1748671370 HTTP/1.1" 200 1182
2025-05-31 06:02:50,677 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:50,970 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/AAVE_USDT?interval=Min1&start=1748668070&end=1748671370 HTTP/1.1" 200 1331
2025-05-31 06:02:51,052 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:51,497 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/SEI_USDT?interval=Min1&start=1748668071&end=1748671371 HTTP/1.1" 200 1003
2025-05-31 06:02:51,583 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:51,860 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/ARB_USDT?interval=Min1&start=1748668071&end=1748671371 HTTP/1.1" 200 1173
2025-05-31 06:02:51,959 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:52,235 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/APT_USDT?interval=Min1&start=1748668071&end=1748671371 HTTP/1.1" 200 1404
2025-05-31 06:02:52,319 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:52,622 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/HBAR_USDT?interval=Min1&start=1748668072&end=1748671372 HTTP/1.1" 200 1350
2025-05-31 06:02:52,713 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:53,021 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/TONCOIN_USDT?interval=Min1&start=1748668072&end=1748671372 HTTP/1.1" 200 1079
2025-05-31 06:02:53,109 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:53,440 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/KAS_USDT?interval=Min1&start=1748668073&end=1748671373 HTTP/1.1" 200 1160
2025-05-31 06:02:53,525 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:53,809 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/JASMY_USDT?interval=Min1&start=1748668073&end=1748671373 HTTP/1.1" 200 1294
2025-05-31 06:02:53,900 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:54,197 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/BNB_USDT?interval=Min1&start=1748668073&end=1748671373 HTTP/1.1" 200 1189
2025-05-31 06:02:54,281 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:54,572 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/ATOM_USDT?interval=Min1&start=1748668074&end=1748671374 HTTP/1.1" 200 1117
2025-05-31 06:02:54,657 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:54,949 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/RENDER_USDT?interval=Min1&start=1748668074&end=1748671374 HTTP/1.1" 200 1073
2025-05-31 06:02:55,032 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:55,313 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/ICP_USDT?interval=Min1&start=1748668075&end=1748671375 HTTP/1.1" 200 1230
2025-05-31 06:02:55,395 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:55,666 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/UNI_USDT?interval=Min1&start=1748668075&end=1748671375 HTTP/1.1" 200 1163
2025-05-31 06:02:55,755 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:56,036 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/WOO_USDT?interval=Min1&start=1748668075&end=1748671375 HTTP/1.1" 200 1208
2025-05-31 06:02:56,118 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:56,427 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/NOT_USDT?interval=Min1&start=1748668076&end=1748671376 HTTP/1.1" 200 1093
2025-05-31 06:02:56,511 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:56,832 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/FILECOIN_USDT?interval=Min1&start=1748668076&end=1748671376 HTTP/1.1" 200 1100
2025-05-31 06:02:56,925 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:57,207 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/TRX_USDT?interval=Min1&start=1748668076&end=1748671376 HTTP/1.1" 200 1143
2025-05-31 06:02:57,317 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:57,656 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/GOAT_USDT?interval=Min1&start=1748668077&end=1748671377 HTTP/1.1" 200 1286
2025-05-31 06:02:57,754 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:58,102 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/BRETT_USDT?interval=Min1&start=1748668077&end=1748671377 HTTP/1.1" 200 1182
2025-05-31 06:02:58,186 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:58,485 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/LDO_USDT?interval=Min1&start=1748668078&end=1748671378 HTTP/1.1" 200 1270
2025-05-31 06:02:58,569 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:58,847 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/XLM_USDT?interval=Min1&start=1748668078&end=1748671378 HTTP/1.1" 200 1206
2025-05-31 06:02:58,931 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:59,218 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/ORDI_USDT?interval=Min1&start=1748668078&end=1748671378 HTTP/1.1" 200 1255
2025-05-31 06:02:59,299 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:59,586 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/ALGO_USDT?interval=Min1&start=1748668079&end=1748671379 HTTP/1.1" 200 997
2025-05-31 06:02:59,669 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:02:59,974 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/JUP_USDT?interval=Min1&start=1748668079&end=1748671379 HTTP/1.1" 200 1138
2025-05-31 06:03:00,056 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:00,350 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/QNT_USDT?interval=Min1&start=1748668080&end=1748671380 HTTP/1.1" 200 1104
2025-05-31 06:03:00,433 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:00,710 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/JTO_USDT?interval=Min1&start=1748668080&end=1748671380 HTTP/1.1" 200 1229
2025-05-31 06:03:00,801 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:01,124 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/GRT_USDT?interval=Min1&start=1748668080&end=1748671380 HTTP/1.1" 200 1237
2025-05-31 06:03:01,204 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:01,535 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/BCH_USDT?interval=Min1&start=1748668081&end=1748671381 HTTP/1.1" 200 1166
2025-05-31 06:03:01,625 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:01,954 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/MOODENG_USDT?interval=Min1&start=1748668081&end=1748671381 HTTP/1.1" 200 1344
2025-05-31 06:03:02,036 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:02,356 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/GRASS_USDT?interval=Min1&start=1748668082&end=1748671382 HTTP/1.1" 200 1219
2025-05-31 06:03:02,445 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:02,759 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/BOME_USDT?interval=Min1&start=1748668082&end=1748671382 HTTP/1.1" 200 1053
2025-05-31 06:03:02,843 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:03,129 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/AGLD_USDT?interval=Min1&start=1748668082&end=1748671382 HTTP/1.1" 200 1189
2025-05-31 06:03:03,210 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:03,508 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/ARKM_USDT?interval=Min1&start=1748668083&end=1748671383 HTTP/1.1" 200 1104
2025-05-31 06:03:03,591 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:03,879 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/APE_USDT?interval=Min1&start=1748668083&end=1748671383 HTTP/1.1" 200 1140
2025-05-31 06:03:03,963 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:04,258 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/GMX_USDT?interval=Min1&start=1748668083&end=1748671383 HTTP/1.1" 200 840
2025-05-31 06:03:04,339 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:04,689 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/ETC_USDT?interval=Min1&start=1748668084&end=1748671384 HTTP/1.1" 200 1145
2025-05-31 06:03:04,773 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:05,063 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/SAGA_USDT?interval=Min1&start=1748668084&end=1748671384 HTTP/1.1" 200 1119
2025-05-31 06:03:05,157 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:05,442 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/AXS_USDT?interval=Min1&start=1748668085&end=1748671385 HTTP/1.1" 200 919
2025-05-31 06:03:05,524 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:05,796 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/SAND_USDT?interval=Min1&start=1748668085&end=1748671385 HTTP/1.1" 200 1277
2025-05-31 06:03:05,881 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:06,211 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/PEOPLE_USDT?interval=Min1&start=1748668085&end=1748671385 HTTP/1.1" 200 1082
2025-05-31 06:03:06,292 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:06,577 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/MKR_USDT?interval=Min1&start=1748668086&end=1748671386 HTTP/1.1" 200 1049
2025-05-31 06:03:06,662 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:06,974 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/BAKE_USDT?interval=Min1&start=1748668086&end=1748671386 HTTP/1.1" 200 923
2025-05-31 06:03:07,053 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:07,356 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/CHZ_USDT?interval=Min1&start=1748668087&end=1748671387 HTTP/1.1" 200 1086
2025-05-31 06:03:07,455 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:07,742 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/KAVA_USDT?interval=Min1&start=1748668087&end=1748671387 HTTP/1.1" 200 1194
2025-05-31 06:03:07,823 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:08,128 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/BLZ_USDT?interval=Min1&start=1748668087&end=1748671387 HTTP/1.1" 200 876
2025-05-31 06:03:08,211 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:08,599 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/MANA_USDT?interval=Min1&start=1748668088&end=1748671388 HTTP/1.1" 200 1022
2025-05-31 06:03:08,696 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:08,975 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/STX_USDT?interval=Min1&start=1748668088&end=1748671388 HTTP/1.1" 200 1012
2025-05-31 06:03:09,055 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:09,355 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/HIFI_USDT?interval=Min1&start=1748668089&end=1748671389 HTTP/1.1" 200 849
2025-05-31 06:03:09,443 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:09,718 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/1INCH_USDT?interval=Min1&start=1748668089&end=1748671389 HTTP/1.1" 200 994
2025-05-31 06:03:09,802 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:10,134 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/YGG_USDT?interval=Min1&start=1748668089&end=1748671389 HTTP/1.1" 200 1020
2025-05-31 06:03:10,217 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:10,497 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/MINA_USDT?interval=Min1&start=1748668090&end=1748671390 HTTP/1.1" 200 1001
2025-05-31 06:03:10,575 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:10,886 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/CRO_USDT?interval=Min1&start=1748668090&end=1748671390 HTTP/1.1" 200 None
2025-05-31 06:03:10,970 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:11,263 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/ZEN_USDT?interval=Min1&start=1748668090&end=1748671390 HTTP/1.1" 200 1157
2025-05-31 06:03:11,344 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:11,641 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/STORJ_USDT?interval=Min1&start=1748668091&end=1748671391 HTTP/1.1" 200 999
2025-05-31 06:03:11,725 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:12,021 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/TRU_USDT?interval=Min1&start=1748668091&end=1748671391 HTTP/1.1" 200 1092
2025-05-31 06:03:12,104 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:12,390 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/FXS_USDT?interval=Min1&start=1748668092&end=1748671392 HTTP/1.1" 200 1003
2025-05-31 06:03:12,474 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:12,766 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/XVG_USDT?interval=Min1&start=1748668092&end=1748671392 HTTP/1.1" 200 1037
2025-05-31 06:03:12,867 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:13,174 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/LQTY_USDT?interval=Min1&start=1748668092&end=1748671392 HTTP/1.1" 200 1137
2025-05-31 06:03:13,255 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:13,554 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/SFP_USDT?interval=Min1&start=1748668093&end=1748671393 HTTP/1.1" 200 1006
2025-05-31 06:03:13,636 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:13,919 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/FLOW_USDT?interval=Min1&start=1748668093&end=1748671393 HTTP/1.1" 200 1074
2025-05-31 06:03:14,003 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:14,301 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/DUSK_USDT?interval=Min1&start=1748668094&end=1748671394 HTTP/1.1" 200 1133
2025-05-31 06:03:14,386 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:14,667 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/BNT_USDT?interval=Min1&start=1748668094&end=1748671394 HTTP/1.1" 200 931
2025-05-31 06:03:14,752 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:15,058 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/RIF_USDT?interval=Min1&start=1748668094&end=1748671394 HTTP/1.1" 200 1026
2025-05-31 06:03:15,142 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:15,499 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/KNC_USDT?interval=Min1&start=1748668095&end=1748671395 HTTP/1.1" 200 1053
2025-05-31 06:03:15,580 - api - WARNING - Bot chưa sẵn sàng
2025-05-31 06:03:30,596 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:30,910 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/BTC_USDT?interval=Min1&start=1748668110&end=1748671410 HTTP/1.1" 200 1453
2025-05-31 06:03:30,998 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:31,279 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/ETH_USDT?interval=Min1&start=1748668110&end=1748671410 HTTP/1.1" 200 1448
2025-05-31 06:03:31,372 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:31,671 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/SOL_USDT?interval=Min1&start=1748668111&end=1748671411 HTTP/1.1" 200 1266
2025-05-31 06:03:31,756 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:32,072 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/SUI_USDT?interval=Min1&start=1748668111&end=1748671411 HTTP/1.1" 200 1401
2025-05-31 06:03:32,158 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:32,442 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/ENA_USDT?interval=Min1&start=1748668112&end=1748671412 HTTP/1.1" 200 1150
2025-05-31 06:03:32,524 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:32,836 - api - DEBUG - https://contract.mexc.com:443 "GET /api/v1/contract/kline/XRP_USDT?interval=Min1&start=1748668112&end=1748671412 HTTP/1.1" 200 1309
2025-05-31 06:03:32,923 - api - DEBUG - Starting new HTTPS connection (1): contract.mexc.com:443
2025-05-31 06:03:33,047 - api - INFO - Nhận tín hiệu dừng từ bàn phím. Tắt bot...
2025-05-31 06:03:33,048 - api - WARNING - Bot chưa sẵn sàng
