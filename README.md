# TradingBot MEXC - Telegram Auto Trading System

## Mục tiêu
- Tự động hóa giao dịch hợp đồng tương lai trên MEXC.
- <PERSON><PERSON><PERSON><PERSON> lý, g<PERSON><PERSON><PERSON> s<PERSON>, điề<PERSON> khiển qua Telegram.
- Hỗ trợ chiến lược tùy chỉnh, c<PERSON><PERSON> báo, quản lý vốn.

## C<PERSON>u trúc thư mục

```
mexcfix/
├── bot.py                  # Lõi bot, quản lý vòng lặp giao dịch, tích hợp Discord
├── trading_strategy.py     # Hàm tính toán chỉ báo, chiến lượ<PERSON> vào/thoát lệnh
├── basic_call.py           # Đặt lệnh, gọi API MEXC (REST)
├── contract_v1_python_demo.py # Giao tiếp API hợp đồng MEXC (REST)
├── discord_handler.py      # Giao tiếp Discord, x<PERSON> lý lệnh, gử<PERSON> thông báo
├── symbol.py               # Quản lý danh sách symbol, factor
├── config.yaml             # File cấu hình hệ thống, token, key, tham số chiến lược
├── setup.py                # Cấu hình package
├── README.md               # Tài liệu
├── docs/                   # Thư mục tài liệu
│   ├── index.md            # Trang chủ tài liệu
│   ├── API.md              # Tài liệu API
│   ├── CHANGELOG.md        # Lịch sử thay đổi
│   └── USAGE_GUIDE.md      # Hướng dẫn sử dụng
├── logs/                   # Thư mục log (trade, logic, error, api)
└── tests/                  # Thư mục kiểm thử
    ├── __init__.py
    ├── conftest.py
    ├── test_cache_optimization.py
    ├── test_code_structure.py
    └── test_integration.py
```

## Yêu cầu hệ thống
- Python 3.8+
- Các package: numpy, pandas, pyyaml, requests, ta-lib, discord.py
- Tài khoản MEXC, API key, Discord bot token

## Hướng dẫn cài đặt

### Cài đặt từ source

```bash
# Clone repository
git clone https://github.com/yourusername/mexcfix.git
cd mexcfix

# Cài đặt dependencies
pip install -e .
```

### Cài đặt TA-Lib

TA-Lib là một thư viện phân tích kỹ thuật được sử dụng trong dự án này. Để cài đặt TA-Lib:

#### Linux

```bash
# Ubuntu/Debian
sudo apt-get install build-essential
sudo apt-get install python3-dev
sudo apt-get install libta-lib0 libta-lib-dev
pip install ta-lib

# Fedora/CentOS
sudo yum install ta-lib-devel
pip install ta-lib
```

#### macOS

```bash
brew install ta-lib
pip install ta-lib
```

#### Windows

Tải xuống file wheel từ [đây](https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib) và cài đặt:

```bash
pip install path/to/downloaded/wheel/file.whl
```

### Cấu hình

Cấu hình file config.yaml:
- Điền các thông tin: `API_KEY`, `SECRET_KEY`, `SIM_KEY`, Discord `token`, `admin_id`, `guild_id`, `channel`, các tham số chiến lược, symbol, volume, leverage, ...

### Khởi động bot

```bash
python3 bot.py
```

## Sử dụng Telegram
- Kết nối Telegram bot với tài khoản quản trị (`TG_ADMIN_ID`).
- Các lệnh hỗ trợ:
  - `/status` - Xem trạng thái bot, thông số tài khoản
  - `/balance` - Xem số dư, equity
  - `/orders` - Xem danh sách lệnh đang mở
  - `/watchlist` - Xem danh sách cặp đang theo dõi
  - `/openpositions` - Xem các vị thế mở
  - `/updatesymbols [số lượng]` - Cập nhật danh sách symbol từ MEXC (mặc định: 20)
  - `/help` - Xem hướng dẫn

## Quy trình hoạt động
1. Bot khởi tạo, nạp cấu hình, kết nối Telegram.
2. Vòng lặp chính:
   - Cập nhật watchlist, kiểm tra điều kiện vào lệnh (RSI, ATR, custom).
   - Đặt lệnh qua API khi đủ điều kiện, quản lý trạng thái lệnh.
   - Gửi thông báo qua Telegram, log chi tiết.
   - Quản lý loại trừ symbol, cảnh báo, tổng kết lệnh định kỳ.
3. Quản lý lỗi: Ghi log, gửi cảnh báo Telegram khi có lỗi.

## Cải tiến mới (v1.1.0)

Xem chi tiết về các cải tiến mới trong [CHANGELOG.md](docs/CHANGELOG.md).

### 1. Loại bỏ mã trùng lặp
- Xóa phiên bản trùng lặp của `get_open_orders_info` trong `bot.py`
- Xóa phiên bản trùng lặp của `handle_watchlist` trong `telegram_handler.py`
- Xóa đăng ký lệnh trùng lặp trong `setup_telegram_commands`

### 2. Tối ưu hóa hiệu suất
- Cải thiện cơ chế cache với TTL khác nhau cho các khung thời gian
- Tối ưu hóa phương thức `get_current_price` để giảm số lượng API call
- Tối ưu hóa phương thức `get_kline_data` để sử dụng cache hiệu quả hơn

### 3. Cải thiện cấu trúc mã nguồn
- Tách phương thức `manage_open_orders` thành các phương thức nhỏ hơn
- Tách phương thức `run` thành các phương thức nhỏ hơn
- Áp dụng nguyên tắc Single Responsibility Principle

### 4. Mở rộng kiểm thử
- Thêm bài kiểm thử cho cơ chế cache đã cải tiến
- Thêm bài kiểm thử cho cấu trúc mã nguồn đã cải tiến
- Thêm bài kiểm thử tích hợp

## Tài liệu

Tài liệu chi tiết có thể được tìm thấy trong thư mục [docs](docs/):

- [Trang chủ tài liệu](docs/index.md) - Tổng quan về hệ thống
- [Hướng dẫn sử dụng](docs/USAGE_GUIDE.md) - Hướng dẫn chi tiết về cách sử dụng bot
- [Tài liệu API](docs/API.md) - Tài liệu chi tiết về các phương thức và lớp
- [Lịch sử thay đổi](docs/CHANGELOG.md) - Lịch sử các thay đổi

## Kiểm thử

Để chạy tất cả các bài kiểm thử:

```bash
python -m unittest discover tests
```

Hoặc chạy từng bài kiểm thử:

```bash
python -m unittest tests/test_cache_optimization.py
python -m unittest tests/test_code_structure.py
python -m unittest tests/test_integration.py
```

## Troubleshooting
- Lỗi kết nối API: kiểm tra key, network, log logs/api.log.
- Lỗi Discord: kiểm tra token, admin_id, guild_id, channel, log logs/logic.log.
- Lỗi cấu hình: kiểm tra format config.yaml, các trường bắt buộc.
- Đặt lệnh không thành công: xem log error, kiểm tra tham số truyền vào.

## Đóng góp & tuân thủ quy tắc
- Tuân thủ quy tắc về phong cách code, kiểm thử, tài liệu.
- Không tạo mới hàm/biến nếu đã có chức năng tương tự.
- Mọi thay đổi lớn phải cập nhật tài liệu, kiểm thử đi kèm.

---

## Xác thực API & Key

- `API_KEY` + `SECRET_KEY`:
  - Dùng cho truy vấn dữ liệu (giá, kline, tài khoản).
- `SIM_KEY`:
  - Dùng cho các thao tác đặt lệnh (order).

**Lưu ý:**
Không dùng chung key giữa các nhóm API. Nếu thiếu hoặc sai key, hệ thống sẽ báo lỗi rõ ràng trong log.
