import requests
import logging
import importlib
import os
from datetime import datetime

# Thi<PERSON>t lập logging
logger = logging.getLogger('ibsymbol')
if not os.path.exists('logs'):
    os.makedirs('logs')
handler = logging.FileHandler('logs/ibsymbol.log')
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.INFO)

# API MEXC lấy danh sách hợp đồng
MEXC_API_URL = "https://contract.mexc.com/api/v1/contract/detail"

EXCLUDED_SYMBOLS = {'DOGE_USDT', 'SHIB_USDT','PEPE_USDT', 'USDC_USDT'}  # Ví dụ: loại trừ DOGE_USDT và SHIB_USDT

def get_contract_info():
    """<PERSON><PERSON><PERSON> danh sách hợp đồng từ API MEXC"""
    try:
        response = requests.get(MEXC_API_URL, timeout=10)
        response.raise_for_status()
        data = response.json()

        if data.get("success") and "data" in data:
            return data["data"]
        else:
            error_msg = f"Lỗi dữ liệu trả về: {data}"
            logger.error(error_msg)
            return []
    except requests.RequestException as e:
        error_msg = f"Lỗi khi gọi API MEXC: {e}"
        logger.error(error_msg)
        return []

def save_top_symbols(max_symbols=20, min_leverage=200, max_contract_size=1000):
    """
    Lấy và lưu danh sách symbol hàng đầu từ MEXC

    Args:
        max_symbols (int): Số lượng symbol tối đa để lưu
        min_leverage (int): Đòn bẩy tối thiểu để lọc
        max_contract_size (int): Kích thước hợp đồng tối đa để lọc

    Returns:
        dict: Kết quả với thông tin về quá trình cập nhật
    """
    result = {
        "success": False,
        "message": "",
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "symbols_count": 0,
        "symbols": []
    }

    contracts = get_contract_info()
    if not contracts:
        result["message"] = "❌ Không thể lấy dữ liệu từ MEXC!"
        logger.error(result["message"])
        return result

    filtered_contracts = [
        contract for contract in contracts
        if contract["symbol"].endswith("USDT")
        and contract.get("maxLeverage", 0) >= min_leverage
        and contract["symbol"] not in EXCLUDED_SYMBOLS
        and contract.get("contractSize", 0) <= max_contract_size
    ]

    sorted_contracts = sorted(filtered_contracts, key=lambda x: x.get("volume24", 0), reverse=True)[:max_symbols]

    top_symbols = [contract["symbol"] for contract in sorted_contracts]
    volume_factors = [contract["contractSize"] for contract in sorted_contracts]

    result["symbols"] = top_symbols
    result["symbols_count"] = len(top_symbols)

    try:
        with open("symbol.py", "w") as f:
            f.write(f'MEXC_SYMBOL = {top_symbols}\n')
            f.write(f'VOLUME_FACTOR = {volume_factors}\n')

        success_msg = f"✅ Đã lưu danh sách {len(top_symbols)} symbol USDT (maxLeverage >= {min_leverage}, VOLUME_FACTOR <= {max_contract_size}) vào symbol.py, loại trừ: {EXCLUDED_SYMBOLS}"
        logger.info(success_msg)
        result["success"] = True
        result["message"] = success_msg
    except Exception as e:
        error_msg = f"❌ Lỗi khi lưu file symbol.py: {e}"
        logger.error(error_msg)
        result["message"] = error_msg

    return result

def reload_symbols():
    """Tải lại module symbol sau khi cập nhật"""
    try:
        import symbol
        importlib.reload(symbol)
        return True
    except Exception as e:
        logger.error(f"Lỗi khi tải lại module symbol: {e}")
        return False

def update_symbols(max_symbols=20, min_leverage=200, max_contract_size=1000):
    """
    Cập nhật danh sách symbol và tải lại module

    Args:
        max_symbols (int): Số lượng symbol tối đa để lưu
        min_leverage (int): Đòn bẩy tối thiểu để lọc
        max_contract_size (int): Kích thước hợp đồng tối đa để lọc

    Returns:
        dict: Kết quả với thông tin về quá trình cập nhật
    """
    result = save_top_symbols(max_symbols, min_leverage, max_contract_size)

    if result["success"]:
        reload_success = reload_symbols()
        if not reload_success:
            result["message"] += "\n⚠️ Lưu ý: Cập nhật thành công nhưng không thể tải lại module. Vui lòng khởi động lại bot để áp dụng thay đổi."

    return result

if __name__ == "__main__":
    result = save_top_symbols()
    print(result["message"])